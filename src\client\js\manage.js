class CogiteonManager {
    constructor() {
        this.mediaFiles = [];
        this.selectedFiles = new Set();
        this.websocket = null;
        this.projectorEnabled = false;

        this.initializeElements();
        this.attachEventListeners();
        this.connectWebSocket();
        this.loadInitialData();
    }

    initializeElements() {
        // Upload elements
        this.uploadArea = document.getElementById('upload-area');
        this.fileInput = document.getElementById('file-input');
        this.browseBtn = document.getElementById('browse-btn');
        this.uploadProgress = document.getElementById('upload-progress');
        this.progressFill = document.getElementById('progress-fill');
        this.progressText = document.getElementById('progress-text');

        // Media elements
        this.mediaList = document.getElementById('media-list');
        this.mediaCount = document.getElementById('media-count');
        this.refreshBtn = document.getElementById('refresh-btn');
        this.selectAllBtn = document.getElementById('select-all-btn');
        this.deleteSelectedBtn = document.getElementById('delete-selected-btn');
        this.sortSelect = document.getElementById('sort-select');

        // Projector elements
        this.projectorSection = document.getElementById('projector-section');
        this.projectorOnBtn = document.getElementById('projector-on-btn');
        this.projectorOffBtn = document.getElementById('projector-off-btn');
        this.projectorStatusBtn = document.getElementById('projector-status-btn');
        this.projectorStatus = document.getElementById('projector-status');

        // System elements
        this.connectionStatus = document.getElementById('connection-status');
        this.serverStatus = document.getElementById('server-status');
        this.mediaDirectory = document.getElementById('media-directory');
        this.diskSpace = document.getElementById('disk-space');
        this.uptime = document.getElementById('uptime');

        // Modal elements
        this.confirmModal = document.getElementById('confirm-modal');
        this.confirmMessage = document.getElementById('confirm-message');
        this.confirmOk = document.getElementById('confirm-ok');
        this.confirmCancel = document.getElementById('confirm-cancel');

        this.toastContainer = document.getElementById('toast-container');
    }

    attachEventListeners() {
        // Upload events
        this.browseBtn.addEventListener('click', () => this.fileInput.click());
        this.fileInput.addEventListener('change', (e) => this.handleFileSelect(e.target.files));

        // Drag and drop
        this.uploadArea.addEventListener('dragover', (e) => this.handleDragOver(e));
        this.uploadArea.addEventListener('dragleave', (e) => this.handleDragLeave(e));
        this.uploadArea.addEventListener('drop', (e) => this.handleDrop(e));
        this.uploadArea.addEventListener('click', () => this.fileInput.click());

        // Media controls
        this.refreshBtn.addEventListener('click', () => this.refreshMediaAndClient());
        this.selectAllBtn.addEventListener('click', () => this.toggleSelectAll());
        this.deleteSelectedBtn.addEventListener('click', () => this.deleteSelectedFiles());
        this.sortSelect.addEventListener('change', () => this.sortMediaFiles());

        // Projector controls
        this.projectorOnBtn.addEventListener('click', () => this.controlProjector('on'));
        this.projectorOffBtn.addEventListener('click', () => this.controlProjector('off'));
        this.projectorStatusBtn.addEventListener('click', () => this.getProjectorStatus());

        // Modal events
        this.confirmCancel.addEventListener('click', () => this.hideConfirmModal());
        this.confirmModal.addEventListener('click', (e) => {
            if (e.target === this.confirmModal) this.hideConfirmModal();
        });
    }

    connectWebSocket() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}`;

        try {
            this.websocket = new WebSocket(wsUrl);

            this.websocket.onopen = () => {
                this.updateConnectionStatus(true);
                this.requestMediaList();
            };

            this.websocket.onmessage = (event) => {
                const message = JSON.parse(event.data);
                this.handleWebSocketMessage(message);
            };

            this.websocket.onclose = () => {
                this.updateConnectionStatus(false);
                setTimeout(() => this.connectWebSocket(), 5000);
            };

            this.websocket.onerror = (error) => {
                console.error('WebSocket error:', error);
                this.updateConnectionStatus(false);
            };
        } catch (error) {
            console.error('Failed to connect WebSocket:', error);
            this.updateConnectionStatus(false);
        }
    }

    handleWebSocketMessage(message) {
        switch (message.type) {
            case 'mediaList':
                this.updateMediaList(message.data);
                break;
            case 'projectorResult':
                this.handleProjectorResult(message.data);
                break;
            default:
                console.log('Unknown WebSocket message:', message);
        }
    }

    requestMediaList() {
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            this.websocket.send(JSON.stringify({ type: 'getMediaList' }));
        }
    }

    async loadInitialData() {
        await Promise.all([
            this.loadMediaFiles(),
            this.loadSystemInfo(),
            this.checkProjectorAvailability()
        ]);
    }

    async refreshMediaAndClient() {
        // Refresh media files locally
        await this.loadMediaFiles();

        // Send refresh command to client player via WebSocket
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            this.websocket.send(JSON.stringify({ type: 'refreshClient' }));
            this.showToast('Media refreshed and client updated', 'success');
        } else {
            this.showToast('Media refreshed (client offline)', 'warning');
        }
    }

    async loadMediaFiles() {
        try {
            const response = await fetch('/api/media');
            const data = await response.json();

            if (data.success) {
                this.updateMediaList(data.data);
            } else {
                this.showToast('Failed to load media files', 'error');
            }
        } catch (error) {
            console.error('Failed to load media files:', error);
            this.showToast('Failed to load media files', 'error');
        }
    }

    async loadSystemInfo() {
        try {
            const response = await fetch('/health');
            const data = await response.json();

            this.serverStatus.textContent = data.status || 'Unknown';
            this.uptime.textContent = this.formatUptime(data.uptime || 0);
            this.mediaDirectory.textContent = '/media'; // From config

            // Update media count
            this.mediaCount.textContent = `${data.mediaFiles || 0} files`;
        } catch (error) {
            console.error('Failed to load system info:', error);
            this.serverStatus.textContent = 'Error';
        }
    }

    async checkProjectorAvailability() {
        try {
            const response = await fetch('/projector/test');
            if (response.status !== 503) {
                this.projectorEnabled = true;
                this.projectorSection.style.display = 'block';
            }
        } catch (error) {
            // Projector not available
        }
    }

    updateConnectionStatus(connected) {
        this.connectionStatus.className = `status-indicator ${connected ? '' : 'disconnected'}`;
        this.connectionStatus.title = connected ? 'Connected' : 'Disconnected';
    }

    updateMediaList(mediaFiles) {
        this.mediaFiles = mediaFiles || [];
        this.selectedFiles.clear();
        this.renderMediaList();
        this.updateMediaCount();
        this.updateDeleteButton();
    }

    renderMediaList() {
        if (this.mediaFiles.length === 0) {
            this.mediaList.innerHTML = `
                <div style="grid-column: 1 / -1; text-align: center; padding: 40px; color: #7f8c8d;">
                    <p>No media files found</p>
                    <p style="font-size: 14px; margin-top: 10px;">Upload some files to get started</p>
                </div>
            `;
            return;
        }

        const sortedFiles = this.getSortedMediaFiles();

        this.mediaList.innerHTML = sortedFiles.map(file => `
            <div class="media-item" data-filename="${file.filename}">
                <div class="media-header">
                    <div>
                        <input type="checkbox" class="media-checkbox" data-filename="${file.filename}">
                        <span class="media-type ${file.type}">${file.type}</span>
                    </div>
                </div>
                <div class="media-filename" title="${file.filename}">${file.filename}</div>
                <div class="media-info">
                    <span>${this.formatFileSize(file.size)}</span>
                    <span>${this.formatDate(file.createdAt)}</span>
                </div>
                ${file.dimensions ? `
                    <div class="media-info">
                        <span>${file.dimensions.width}×${file.dimensions.height}</span>
                        ${file.duration ? `<span>${this.formatDuration(file.duration)}</span>` : ''}
                    </div>
                ` : ''}
                <div class="media-actions">
                    <button class="btn btn-danger" onclick="cogiteonManager.deleteFile('${file.filename}')">Delete</button>
                </div>
            </div>
        `).join('');

        // Attach checkbox event listeners
        this.mediaList.querySelectorAll('.media-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', (e) => this.handleFileSelection(e));
        });
    }

    getSortedMediaFiles() {
        const sortBy = this.sortSelect.value;
        const files = [...this.mediaFiles];

        return files.sort((a, b) => {
            switch (sortBy) {
                case 'name':
                    return a.filename.localeCompare(b.filename);
                case 'date':
                    return new Date(b.createdAt) - new Date(a.createdAt);
                case 'size':
                    return b.size - a.size;
                case 'type':
                    return a.type.localeCompare(b.type);
                default:
                    return 0;
            }
        });
    }

    handleFileSelection(event) {
        const filename = event.target.dataset.filename;
        const mediaItem = event.target.closest('.media-item');

        if (event.target.checked) {
            this.selectedFiles.add(filename);
            mediaItem.classList.add('selected');
        } else {
            this.selectedFiles.delete(filename);
            mediaItem.classList.remove('selected');
        }

        this.updateDeleteButton();
    }

    toggleSelectAll() {
        const allSelected = this.selectedFiles.size === this.mediaFiles.length;

        if (allSelected) {
            this.selectedFiles.clear();
            this.mediaList.querySelectorAll('.media-checkbox').forEach(cb => cb.checked = false);
            this.mediaList.querySelectorAll('.media-item').forEach(item => item.classList.remove('selected'));
        } else {
            this.mediaFiles.forEach(file => this.selectedFiles.add(file.filename));
            this.mediaList.querySelectorAll('.media-checkbox').forEach(cb => cb.checked = true);
            this.mediaList.querySelectorAll('.media-item').forEach(item => item.classList.add('selected'));
        }

        this.updateDeleteButton();
    }

    updateDeleteButton() {
        this.deleteSelectedBtn.disabled = this.selectedFiles.size === 0;
        this.deleteSelectedBtn.textContent = `Delete Selected (${this.selectedFiles.size})`;
    }

    updateMediaCount() {
        this.mediaCount.textContent = `${this.mediaFiles.length} files`;
    }

    sortMediaFiles() {
        this.renderMediaList();
    }

    // File upload methods
    handleDragOver(event) {
        event.preventDefault();
        this.uploadArea.classList.add('dragover');
    }

    handleDragLeave(event) {
        event.preventDefault();
        this.uploadArea.classList.remove('dragover');
    }

    handleDrop(event) {
        event.preventDefault();
        this.uploadArea.classList.remove('dragover');
        const files = event.dataTransfer.files;
        this.handleFileSelect(files);
    }

    handleFileSelect(files) {
        if (files.length === 0) return;

        const fileArray = Array.from(files);
        this.uploadFiles(fileArray);
    }

    async uploadFiles(files) {
        this.showUploadProgress();

        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            const progress = ((i + 1) / files.length) * 100;

            this.updateUploadProgress(progress, `Uploading ${file.name}...`);

            try {
                await this.uploadSingleFile(file);
                this.showToast(`Uploaded: ${file.name}`, 'success');
            } catch (error) {
                this.showToast(`Failed to upload: ${file.name}`, 'error');
            }
        }

        this.hideUploadProgress();
        this.loadMediaFiles();
    }

    uploadSingleFile(file) {
        return new Promise((resolve, reject) => {
            const formData = new FormData();
            formData.append('media', file);

            const xhr = new XMLHttpRequest();

            xhr.upload.onprogress = (event) => {
                if (event.lengthComputable) {
                    const progress = (event.loaded / event.total) * 100;
                    this.updateUploadProgress(progress, `Uploading ${file.name}...`);
                }
            };

            xhr.onload = () => {
                if (xhr.status === 200) {
                    resolve(JSON.parse(xhr.responseText));
                } else {
                    reject(new Error(`Upload failed: ${xhr.statusText}`));
                }
            };

            xhr.onerror = () => reject(new Error('Upload failed'));

            xhr.open('POST', '/upload');
            xhr.send(formData);
        });
    }

    showUploadProgress() {
        this.uploadProgress.style.display = 'block';
    }

    hideUploadProgress() {
        this.uploadProgress.style.display = 'none';
        this.updateUploadProgress(0, '');
    }

    updateUploadProgress(percent, text) {
        this.progressFill.style.width = `${percent}%`;
        this.progressText.textContent = text;
    }

    // File deletion methods
    deleteSelectedFiles() {
        if (this.selectedFiles.size === 0) return;

        const fileList = Array.from(this.selectedFiles).join(', ');
        this.showConfirmModal(
            `Delete ${this.selectedFiles.size} file(s)?`,
            `Are you sure you want to delete: ${fileList}`,
            () => this.confirmDeleteSelected()
        );
    }

    async confirmDeleteSelected() {
        const filesToDelete = Array.from(this.selectedFiles);

        for (const filename of filesToDelete) {
            try {
                await this.deleteFile(filename, false);
            } catch (error) {
                this.showToast(`Failed to delete: ${filename}`, 'error');
            }
        }

        this.selectedFiles.clear();
        this.loadMediaFiles();
        this.showToast(`Deleted ${filesToDelete.length} files`, 'success');
    }

    async deleteFile(filename, showConfirm = true) {
        if (showConfirm) {
            this.showConfirmModal(
                'Delete file?',
                `Are you sure you want to delete: ${filename}`,
                () => this.confirmDeleteFile(filename)
            );
            return;
        }

        const response = await fetch(`/api/media/${encodeURIComponent(filename)}`, {
            method: 'DELETE'
        });

        if (!response.ok) {
            throw new Error(`Failed to delete file: ${response.statusText}`);
        }

        return response.json();
    }

    async confirmDeleteFile(filename) {
        try {
            await this.deleteFile(filename, false);
            this.loadMediaFiles();
            this.showToast(`Deleted: ${filename}`, 'success');
        } catch (error) {
            this.showToast(`Failed to delete: ${filename}`, 'error');
        }
    }

    // Projector control methods
    async controlProjector(action) {
        if (!this.projectorEnabled) return;

        try {
            const response = await fetch(`/projector/${action}`, { method: 'POST' });
            const data = await response.json();

            if (data.success) {
                this.showToast(data.message || `Projector ${action} successful`, 'success');
            } else {
                this.showToast(data.error || `Projector ${action} failed`, 'error');
            }
        } catch (error) {
            this.showToast(`Projector ${action} failed`, 'error');
        }
    }

    async getProjectorStatus() {
        if (!this.projectorEnabled) return;

        try {
            const response = await fetch('/projector/status');
            const data = await response.json();

            if (data.success) {
                this.projectorStatus.innerHTML = `
                    <strong>Status:</strong> ${JSON.stringify(data.data, null, 2)}
                `;
            } else {
                this.projectorStatus.textContent = 'Failed to get status';
            }
        } catch (error) {
            this.projectorStatus.textContent = 'Error getting status';
        }
    }

    handleProjectorResult(result) {
        if (result.success) {
            this.showToast(result.message || 'Projector command successful', 'success');
        } else {
            this.showToast(result.error || 'Projector command failed', 'error');
        }
    }

    // Modal methods
    showConfirmModal(title, message, onConfirm) {
        this.confirmMessage.textContent = message;
        this.confirmModal.style.display = 'flex';

        this.confirmOk.onclick = () => {
            this.hideConfirmModal();
            onConfirm();
        };
    }

    hideConfirmModal() {
        this.confirmModal.style.display = 'none';
    }

    // Toast notification methods
    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.textContent = message;

        this.toastContainer.appendChild(toast);

        setTimeout(() => {
            toast.remove();
        }, 5000);
    }

    // Utility methods
    formatFileSize(bytes) {
        const units = ['B', 'KB', 'MB', 'GB'];
        let size = bytes;
        let unitIndex = 0;

        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }

        return `${size.toFixed(1)} ${units[unitIndex]}`;
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString();
    }

    formatDuration(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }

    formatUptime(seconds) {
        const days = Math.floor(seconds / 86400);
        const hours = Math.floor((seconds % 86400) / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);

        if (days > 0) {
            return `${days}d ${hours}h ${minutes}m`;
        } else if (hours > 0) {
            return `${hours}h ${minutes}m`;
        } else {
            return `${minutes}m`;
        }
    }
}

// Initialize the manager when the page loads
let cogiteonManager;
document.addEventListener('DOMContentLoaded', () => {
    cogiteonManager = new CogiteonManager();
});
