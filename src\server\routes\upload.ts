import { Router, Request, Response } from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { MediaService } from '../services/media';
import { ConfigService } from '../services/config';
import { LoggerService } from '../services/logger';
import { ApiResponse, UploadResponse } from '../../types';

const router = Router();

// Initialize services
const config = ConfigService.loadConfig();
const logger = LoggerService.createLogger(config.logging);
const mediaService = new MediaService(config.server, logger);

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = config.server.mediaDirectory;

    // Ensure directory exists
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    // Generate unique filename while preserving extension
    const ext = path.extname(file.originalname);
    const name = path.basename(file.originalname, ext);
    const timestamp = Date.now();
    const uniqueName = `${name}_${timestamp}${ext}`;
    cb(null, uniqueName);
  }
});

// File filter
const fileFilter = (req: any, file: Express.Multer.File, cb: any) => {
  logger.info(`File filter check: ${file.originalname}`, {
    mimetype: file.mimetype,
    size: file.size
  });

  const mediaService = new MediaService(config.server, logger);
  const validation = mediaService.validateUploadedFile(file);

  if (validation.valid) {
    logger.info(`File validation passed: ${file.originalname}`);
    cb(null, true);
  } else {
    logger.error(`File validation failed: ${file.originalname}`, { error: validation.error });
    cb(new Error(validation.error), false);
  }
};

// Configure multer
const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: parseSize(config.server.maxFileSize),
    files: 1
  }
});

/**
 * Parse size string to bytes
 */
function parseSize(sizeStr: string): number {
  const units: { [key: string]: number } = {
    'B': 1,
    'KB': 1024,
    'MB': 1024 * 1024,
    'GB': 1024 * 1024 * 1024
  };

  const match = sizeStr.match(/^(\d+)\s*(B|KB|MB|GB)$/i);
  if (!match) {
    return 100 * 1024 * 1024; // Default 100MB
  }

  const [, size, unit] = match;
  return parseInt(size) * units[unit.toUpperCase()];
}

/**
 * Upload media file
 */
router.post('/', upload.single('media'), async (req: Request, res: Response) => {
  try {
    logger.info('Upload request received', {
      hasFile: !!req.file,
      contentType: req.get('Content-Type'),
      contentLength: req.get('Content-Length')
    });

    if (!req.file) {
      logger.error('No file in upload request');
      const response: UploadResponse = {
        success: false,
        message: 'No file uploaded',
        error: 'No file provided'
      };
      return res.status(400).json(response);
    }

    logger.info(`File uploaded: ${req.file.filename}`, {
      originalName: req.file.originalname,
      size: req.file.size,
      mimetype: req.file.mimetype
    });

    // Get file info
    const mediaFiles = await mediaService.getMediaFiles();
    const uploadedFile = mediaFiles.find(file => file.filename === req.file!.filename);

    const response: UploadResponse = {
      success: true,
      message: 'File uploaded successfully',
      file: uploadedFile
    };

    res.json(response);
  } catch (error) {
    logger.error('Upload error:', error);

    // Clean up file if it was created
    if (req.file && fs.existsSync(req.file.path)) {
      try {
        fs.unlinkSync(req.file.path);
      } catch (cleanupError) {
        logger.error('Failed to cleanup uploaded file:', cleanupError);
      }
    }

    const response: UploadResponse = {
      success: false,
      message: 'Upload failed',
      error: error.message
    };

    res.status(500).json(response);
  }
});

/**
 * Upload multiple files
 */
router.post('/multiple', upload.array('media', 10), async (req: Request, res: Response) => {
  try {
    const files = req.files as Express.Multer.File[];

    if (!files || files.length === 0) {
      const response: ApiResponse = {
        success: false,
        error: 'No files uploaded'
      };
      return res.status(400).json(response);
    }

    logger.info(`Multiple files uploaded: ${files.length} files`);

    // Get updated media files list
    const mediaFiles = await mediaService.getMediaFiles();
    const uploadedFiles = mediaFiles.filter(file =>
      files.some(uploadedFile => uploadedFile.filename === file.filename)
    );

    const response: ApiResponse = {
      success: true,
      message: `${files.length} files uploaded successfully`,
      data: {
        count: files.length,
        files: uploadedFiles
      }
    };

    res.json(response);
  } catch (error) {
    logger.error('Multiple upload error:', error);

    // Clean up files if they were created
    const files = req.files as Express.Multer.File[];
    if (files) {
      for (const file of files) {
        if (fs.existsSync(file.path)) {
          try {
            fs.unlinkSync(file.path);
          } catch (cleanupError) {
            logger.error('Failed to cleanup uploaded file:', cleanupError);
          }
        }
      }
    }

    const response: ApiResponse = {
      success: false,
      error: 'Multiple upload failed'
    };

    res.status(500).json(response);
  }
});

/**
 * Get upload configuration
 */
router.get('/config', (req: Request, res: Response) => {
  const response: ApiResponse = {
    success: true,
    data: {
      maxFileSize: config.server.maxFileSize,
      allowedExtensions: config.server.allowedExtensions,
      mediaDirectory: config.server.mediaDirectory
    }
  };
  res.json(response);
});

/**
 * Error handler for multer
 */
router.use((error: any, req: Request, res: Response, next: any) => {
  if (error instanceof multer.MulterError) {
    let message = 'Upload error';

    switch (error.code) {
      case 'LIMIT_FILE_SIZE':
        message = `File too large. Maximum size is ${config.server.maxFileSize}`;
        break;
      case 'LIMIT_FILE_COUNT':
        message = 'Too many files';
        break;
      case 'LIMIT_UNEXPECTED_FILE':
        message = 'Unexpected file field';
        break;
      default:
        message = error.message;
    }

    const response: UploadResponse = {
      success: false,
      message,
      error: error.code
    };

    return res.status(400).json(response);
  }

  if (error.message) {
    const response: UploadResponse = {
      success: false,
      message: 'Upload failed',
      error: error.message
    };
    return res.status(400).json(response);
  }

  next(error);
});

export default router;
