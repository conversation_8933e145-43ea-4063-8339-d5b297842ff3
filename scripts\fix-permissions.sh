#!/bin/bash

# Cogiteon Player Permission Fix Script
# Run this on the Raspberry Pi to fix file upload permission issues
# Usage: bash scripts/fix-permissions.sh
# Or with sudo: sudo bash scripts/fix-permissions.sh

echo "🔧 Cogiteon Player Permission Fix"
echo "================================="

# Get current user and project directory
CURRENT_USER=$(whoami)
PROJECT_DIR=$(pwd)
MEDIA_DIR="$PROJECT_DIR/media"
DIST_DIR="$PROJECT_DIR/dist"
LOGS_DIR="$PROJECT_DIR/logs"

echo "📁 Current user: $CURRENT_USER"
echo "📁 Project directory: $PROJECT_DIR"
echo "📁 Media directory: $MEDIA_DIR"

# Function to check and create directory with proper permissions
check_and_fix_directory() {
    local dir_path="$1"
    local dir_name="$2"

    echo ""
    echo "🔍 Checking $dir_name directory: $dir_path"

    if [ ! -d "$dir_path" ]; then
        echo "❌ Directory doesn't exist, creating..."
        mkdir -p "$dir_path"
        if [ $? -eq 0 ]; then
            echo "✅ Created directory: $dir_path"
        else
            echo "❌ Failed to create directory: $dir_path"
            return 1
        fi
    else
        echo "✅ Directory exists: $dir_path"
    fi

    # Check current permissions
    echo "📊 Current permissions:"
    ls -la "$dir_path" | head -1
    ls -lad "$dir_path"

    # Set proper permissions
    echo "🔧 Setting permissions..."
    chmod 755 "$dir_path"
    chown "$CURRENT_USER:$CURRENT_USER" "$dir_path" 2>/dev/null || echo "⚠️  Could not change ownership (may need sudo)"

    # Test write access
    echo "🧪 Testing write access..."
    TEST_FILE="$dir_path/.write-test-$$"
    if echo "test" > "$TEST_FILE" 2>/dev/null; then
        rm -f "$TEST_FILE"
        echo "✅ Write access confirmed"
        return 0
    else
        echo "❌ No write access!"
        return 1
    fi
}

# Function to fix file permissions
fix_file_permissions() {
    local file_path="$1"
    local file_name="$2"

    if [ -f "$file_path" ]; then
        echo "🔧 Fixing permissions for $file_name: $file_path"
        chmod 644 "$file_path"
        chown "$CURRENT_USER:$CURRENT_USER" "$file_path" 2>/dev/null || echo "⚠️  Could not change ownership for $file_name"
        echo "✅ Fixed permissions for $file_name"
    else
        echo "⚠️  File not found: $file_path"
    fi
}

echo ""
echo "🔍 System Information:"
echo "OS: $(uname -a)"
echo "Node.js version: $(node --version 2>/dev/null || echo 'Not found')"
echo "NPM version: $(npm --version 2>/dev/null || echo 'Not found')"
echo "Current working directory: $(pwd)"
echo "User ID: $(id)"

# Check and fix directories
echo ""
echo "📁 Checking and fixing directories..."

check_and_fix_directory "$MEDIA_DIR" "Media"
MEDIA_OK=$?

check_and_fix_directory "$DIST_DIR" "Dist"
DIST_OK=$?

check_and_fix_directory "$LOGS_DIR" "Logs"
LOGS_OK=$?

# Fix specific file permissions
echo ""
echo "📄 Fixing file permissions..."

fix_file_permissions "$PROJECT_DIR/package.json" "package.json"
fix_file_permissions "$PROJECT_DIR/config/config.json" "config.json"

# Fix dist client files if they exist
if [ -d "$DIST_DIR/client" ]; then
    echo "🔧 Fixing client file permissions..."
    find "$DIST_DIR/client" -type f -exec chmod 644 {} \;
    find "$DIST_DIR/client" -type d -exec chmod 755 {} \;
    echo "✅ Fixed client file permissions"
fi

# Fix media files if they exist
if [ -d "$MEDIA_DIR" ] && [ "$(ls -A $MEDIA_DIR)" ]; then
    echo "🔧 Fixing existing media file permissions..."
    find "$MEDIA_DIR" -type f -exec chmod 644 {} \;
    echo "✅ Fixed media file permissions"
fi

# Check Node.js process permissions
echo ""
echo "🔍 Node.js Process Check:"
if command -v node >/dev/null 2>&1; then
    echo "✅ Node.js is available"

    # Test if we can run node
    if node -e "console.log('Node.js test successful')" 2>/dev/null; then
        echo "✅ Node.js execution test passed"
    else
        echo "❌ Node.js execution test failed"
    fi

    # Test file system access from Node.js
    node -e "
        const fs = require('fs');
        const path = require('path');
        const testDir = '$MEDIA_DIR';
        try {
            if (!fs.existsSync(testDir)) {
                console.log('❌ Media directory not accessible from Node.js');
                process.exit(1);
            }
            const testFile = path.join(testDir, '.node-test');
            fs.writeFileSync(testFile, 'test');
            fs.unlinkSync(testFile);
            console.log('✅ Node.js can write to media directory');
        } catch (error) {
            console.log('❌ Node.js cannot write to media directory:', error.message);
            process.exit(1);
        }
    "
    NODE_TEST=$?
else
    echo "❌ Node.js not found"
    NODE_TEST=1
fi

# Summary
echo ""
echo "📋 Summary:"
echo "==========="

if [ $MEDIA_OK -eq 0 ]; then
    echo "✅ Media directory permissions: OK"
else
    echo "❌ Media directory permissions: FAILED"
fi

if [ $DIST_OK -eq 0 ]; then
    echo "✅ Dist directory permissions: OK"
else
    echo "❌ Dist directory permissions: FAILED"
fi

if [ $NODE_TEST -eq 0 ]; then
    echo "✅ Node.js file access: OK"
else
    echo "❌ Node.js file access: FAILED"
fi

# Recommendations
echo ""
echo "💡 Recommendations:"

if [ $MEDIA_OK -ne 0 ] || [ $NODE_TEST -ne 0 ]; then
    echo "❌ Permission issues detected!"
    echo ""
    echo "🔧 Try these fixes:"
    echo "   1. Run with sudo: sudo $0"
    echo "   2. Change ownership: sudo chown -R $CURRENT_USER:$CURRENT_USER $PROJECT_DIR"
    echo "   3. Fix permissions: sudo chmod -R 755 $PROJECT_DIR"
    echo "   4. Restart the application: npm start"
    echo ""
    echo "🔍 Debug commands:"
    echo "   ls -la $MEDIA_DIR"
    echo "   whoami"
    echo "   id"
    echo "   ps aux | grep node"
else
    echo "✅ All permissions look good!"
    echo ""
    echo "🚀 Next steps:"
    echo "   1. Restart the server: npm start"
    echo "   2. Test file upload via management interface"
    echo "   3. Check server logs: tail -f logs/app.log"
fi

echo ""
echo "🔧 Manual permission commands if needed:"
echo "   sudo chown -R $CURRENT_USER:$CURRENT_USER $PROJECT_DIR"
echo "   sudo chmod -R 755 $PROJECT_DIR"
echo "   sudo chmod 755 $MEDIA_DIR"
echo "   sudo chmod 644 $MEDIA_DIR/*"
