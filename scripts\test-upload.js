#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const FormData = require('form-data');
const http = require('http');

/**
 * Test script to diagnose upload issues
 * Creates a test image and tries to upload it to the server
 */

function createTestImage() {
    // Create a simple 1x1 PNG image (base64 encoded)
    const pngData = Buffer.from(
        'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77yQAAAABJRU5ErkJggg==',
        'base64'
    );
    
    const testImagePath = path.join(__dirname, 'test-image.png');
    fs.writeFileSync(testImagePath, pngData);
    console.log(`✅ Created test image: ${testImagePath}`);
    return testImagePath;
}

function testUpload(imagePath, port = 3000) {
    return new Promise((resolve, reject) => {
        const form = new FormData();
        form.append('media', fs.createReadStream(imagePath));
        
        const options = {
            hostname: 'localhost',
            port: port,
            path: '/upload',
            method: 'POST',
            headers: form.getHeaders()
        };
        
        console.log(`🚀 Testing upload to http://localhost:${port}/upload`);
        
        const req = http.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                console.log(`📊 Response status: ${res.statusCode}`);
                console.log(`📊 Response headers:`, res.headers);
                console.log(`📊 Response body:`, data);
                
                if (res.statusCode === 200) {
                    resolve({ success: true, data: JSON.parse(data) });
                } else {
                    resolve({ success: false, status: res.statusCode, data });
                }
            });
        });
        
        req.on('error', (error) => {
            console.error(`❌ Request error:`, error);
            reject(error);
        });
        
        form.pipe(req);
    });
}

function testEndpoints(port = 3000) {
    const endpoints = [
        '/health',
        '/api/media',
        '/upload/config',
        '/static/css/manage.css'
    ];
    
    console.log(`\n🔍 Testing endpoints on port ${port}:`);
    
    endpoints.forEach(endpoint => {
        const options = {
            hostname: 'localhost',
            port: port,
            path: endpoint,
            method: 'GET'
        };
        
        const req = http.request(options, (res) => {
            console.log(`${res.statusCode === 200 ? '✅' : '❌'} ${endpoint}: ${res.statusCode} ${res.statusMessage}`);
        });
        
        req.on('error', (error) => {
            console.log(`❌ ${endpoint}: ${error.message}`);
        });
        
        req.end();
    });
}

async function main() {
    console.log('🧪 Cogiteon Player Upload Test\n');
    
    // Test basic endpoints first
    testEndpoints(3000);
    
    // Wait a bit for endpoint tests
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    console.log('\n📤 Testing file upload:');
    
    try {
        // Create test image
        const testImagePath = createTestImage();
        
        // Test upload
        const result = await testUpload(testImagePath, 3000);
        
        if (result.success) {
            console.log('🎉 Upload test successful!');
            console.log('📄 Upload response:', JSON.stringify(result.data, null, 2));
        } else {
            console.log('❌ Upload test failed');
            console.log(`📄 Status: ${result.status}`);
            console.log(`📄 Response: ${result.data}`);
        }
        
        // Clean up test file
        fs.unlinkSync(testImagePath);
        console.log('🧹 Cleaned up test image');
        
    } catch (error) {
        console.error('❌ Upload test error:', error.message);
    }
    
    console.log('\n💡 If upload fails, check:');
    console.log('   1. Server is running on the correct port');
    console.log('   2. Media directory exists and is writable');
    console.log('   3. File validation settings in config');
    console.log('   4. Server logs for detailed error messages');
    console.log('\n🔧 Useful commands:');
    console.log('   tail -f logs/app.log     # Watch server logs');
    console.log('   ls -la media/            # Check media directory');
    console.log('   curl -X POST -F "media=@test.png" http://localhost:3000/upload');
}

if (require.main === module) {
    main().catch(console.error);
}
