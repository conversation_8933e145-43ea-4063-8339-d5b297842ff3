#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * Fix script for static file serving issues
 * This script will rebuild the project and ensure client files are properly copied
 */

function runCommand(command, description) {
    console.log(`🔨 ${description}...`);
    try {
        execSync(command, { stdio: 'inherit' });
        console.log(`✅ ${description} completed`);
        return true;
    } catch (error) {
        console.log(`❌ ${description} failed:`, error.message);
        return false;
    }
}

function ensureDirectory(dirPath) {
    if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
        console.log(`📁 Created directory: ${dirPath}`);
    }
}

function copyFile(src, dest) {
    try {
        ensureDirectory(path.dirname(dest));
        fs.copyFileSync(src, dest);
        console.log(`📄 Copied: ${path.relative(process.cwd(), src)} -> ${path.relative(process.cwd(), dest)}`);
        return true;
    } catch (error) {
        console.log(`❌ Failed to copy ${src} to ${dest}:`, error.message);
        return false;
    }
}

function copyDirectory(srcDir, destDir) {
    if (!fs.existsSync(srcDir)) {
        console.log(`❌ Source directory does not exist: ${srcDir}`);
        return false;
    }
    
    ensureDirectory(destDir);
    
    const items = fs.readdirSync(srcDir);
    let success = true;
    
    for (const item of items) {
        const srcPath = path.join(srcDir, item);
        const destPath = path.join(destDir, item);
        
        const stat = fs.statSync(srcPath);
        
        if (stat.isDirectory()) {
            if (!copyDirectory(srcPath, destPath)) {
                success = false;
            }
        } else {
            if (!copyFile(srcPath, destPath)) {
                success = false;
            }
        }
    }
    
    return success;
}

function main() {
    console.log('🚀 Fixing Cogiteon Player Static Files...\n');
    
    const projectRoot = process.cwd();
    const srcClientDir = path.join(projectRoot, 'src', 'client');
    const distClientDir = path.join(projectRoot, 'dist', 'client');
    
    // Step 1: Clean dist directory
    console.log('🧹 Cleaning dist directory...');
    if (fs.existsSync(distClientDir)) {
        try {
            fs.rmSync(distClientDir, { recursive: true, force: true });
            console.log('✅ Cleaned dist/client directory');
        } catch (error) {
            console.log('⚠️  Could not clean dist/client directory:', error.message);
        }
    }
    
    // Step 2: Compile TypeScript
    console.log('\n📦 Compiling TypeScript...');
    const tsSuccess = runCommand('npx tsc --noEmitOnError false', 'TypeScript compilation');
    
    // Step 3: Copy client files
    console.log('\n📁 Copying client files...');
    const copySuccess = copyDirectory(srcClientDir, distClientDir);
    
    if (copySuccess) {
        console.log('✅ Client files copied successfully');
    } else {
        console.log('❌ Some client files failed to copy');
    }
    
    // Step 4: Verify files exist
    console.log('\n🔍 Verifying files...');
    const requiredFiles = [
        'dist/client/manage.html',
        'dist/client/player.html',
        'dist/client/css/manage.css',
        'dist/client/js/manage.js'
    ];
    
    let allFilesExist = true;
    for (const file of requiredFiles) {
        const filePath = path.join(projectRoot, file);
        const exists = fs.existsSync(filePath);
        console.log(`${exists ? '✅' : '❌'} ${file}`);
        if (!exists) allFilesExist = false;
    }
    
    // Step 5: Show results
    console.log('\n📋 Results:');
    if (tsSuccess && copySuccess && allFilesExist) {
        console.log('🎉 All fixes applied successfully!');
        console.log('\n📋 Next steps:');
        console.log('   1. Restart the server: npm start');
        console.log('   2. Test management interface: http://your-pi-ip:3000/manage');
        console.log('   3. Check browser developer tools for any remaining errors');
    } else {
        console.log('⚠️  Some issues remain. Check the errors above.');
        if (!tsSuccess) console.log('   - TypeScript compilation had issues');
        if (!copySuccess) console.log('   - Client file copying had issues');
        if (!allFilesExist) console.log('   - Some required files are missing');
    }
    
    console.log('\n🔧 Manual verification commands:');
    console.log('   node scripts/diagnose-static-files.js  # Run diagnostics');
    console.log('   curl http://localhost:3000/static/css/manage.css  # Test CSS access');
    console.log('   curl http://localhost:3000/static/js/manage.js   # Test JS access');
}

if (require.main === module) {
    main();
}
