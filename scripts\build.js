#!/usr/bin/env node

const { execSync } = require('child_process');
const { copyDirectory } = require('./copy-client');

/**
 * Build script that compiles TypeScript and copies client files
 * This script continues even if TypeScript compilation has warnings/errors
 */

function runCommand(command, description) {
    console.log(`\n🔨 ${description}...`);
    try {
        execSync(command, { stdio: 'inherit' });
        console.log(`✅ ${description} completed`);
        return true;
    } catch (error) {
        console.log(`⚠️  ${description} completed with warnings/errors`);
        return false;
    }
}

function main() {
    console.log('🚀 Starting Cogiteon Player build...');
    
    // Step 1: Compile TypeScript (continue even with errors)
    const tsSuccess = runCommand('npx tsc --noEmitOnError false', 'TypeScript compilation');
    
    // Step 2: Copy client files
    console.log('\n📁 Copying client files...');
    try {
        const { copyDirectory } = require('./copy-client');
        const path = require('path');
        
        const srcDir = path.join(__dirname, '..', 'src', 'client');
        const destDir = path.join(__dirname, '..', 'dist', 'client');
        
        copyDirectory(srcDir, destDir);
        console.log('✅ Client files copied successfully');
    } catch (error) {
        console.error('❌ Failed to copy client files:', error.message);
        process.exit(1);
    }
    
    console.log('\n🎉 Build completed!');
    
    if (!tsSuccess) {
        console.log('⚠️  Note: TypeScript compilation had warnings/errors, but JavaScript files were still generated.');
        console.log('   The application should still work. Consider fixing TypeScript issues for better code quality.');
    }
    
    console.log('\n📋 Next steps:');
    console.log('   • Run "npm start" to start the server');
    console.log('   • Run "npm run dev" for development mode');
}

if (require.main === module) {
    main();
}
