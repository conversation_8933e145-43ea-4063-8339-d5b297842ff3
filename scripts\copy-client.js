#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * Copy client files from src/client to dist/client
 * This script ensures that HTML, CSS, and JS files are available for the server
 */

const srcDir = path.join(__dirname, '..', 'src', 'client');
const destDir = path.join(__dirname, '..', 'dist', 'client');

function ensureDirectoryExists(dirPath) {
    if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
        console.log(`Created directory: ${dirPath}`);
    }
}

function copyFile(src, dest) {
    try {
        fs.copyFileSync(src, dest);
        console.log(`Copied: ${path.relative(process.cwd(), src)} -> ${path.relative(process.cwd(), dest)}`);
    } catch (error) {
        console.error(`Failed to copy ${src} to ${dest}:`, error.message);
    }
}

function copyDirectory(srcDir, destDir) {
    ensureDirectoryExists(destDir);
    
    const items = fs.readdirSync(srcDir);
    
    for (const item of items) {
        const srcPath = path.join(srcDir, item);
        const destPath = path.join(destDir, item);
        
        const stat = fs.statSync(srcPath);
        
        if (stat.isDirectory()) {
            copyDirectory(srcPath, destPath);
        } else {
            copyFile(srcPath, destPath);
        }
    }
}

function main() {
    console.log('Copying client files...');
    
    if (!fs.existsSync(srcDir)) {
        console.error(`Source directory does not exist: ${srcDir}`);
        process.exit(1);
    }
    
    try {
        copyDirectory(srcDir, destDir);
        console.log('Client files copied successfully!');
    } catch (error) {
        console.error('Failed to copy client files:', error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = { copyDirectory, ensureDirectoryExists };
