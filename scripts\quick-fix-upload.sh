#!/bin/bash

# Quick fix for upload permission issues on Raspberry Pi
# This script fixes the most common permission problems

echo "🚀 Quick Upload Permission Fix"
echo "=============================="

PROJECT_DIR=$(pwd)
MEDIA_DIR="$PROJECT_DIR/media"

echo "📁 Project: $PROJECT_DIR"
echo "📁 Media: $MEDIA_DIR"
echo "👤 User: $(whoami)"

# Create media directory if it doesn't exist
echo ""
echo "1️⃣ Creating media directory..."
mkdir -p "$MEDIA_DIR"
if [ $? -eq 0 ]; then
    echo "✅ Media directory ready"
else
    echo "❌ Failed to create media directory"
    exit 1
fi

# Fix ownership
echo ""
echo "2️⃣ Fixing ownership..."
chown -R $(whoami):$(whoami) "$PROJECT_DIR" 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✅ Ownership fixed"
else
    echo "⚠️  Could not change ownership (trying with sudo...)"
    sudo chown -R $(whoami):$(whoami) "$PROJECT_DIR"
    if [ $? -eq 0 ]; then
        echo "✅ Ownership fixed with sudo"
    else
        echo "❌ Could not fix ownership"
    fi
fi

# Fix permissions
echo ""
echo "3️⃣ Fixing permissions..."
chmod 755 "$MEDIA_DIR"
chmod 755 "$PROJECT_DIR"
if [ -d "$PROJECT_DIR/dist" ]; then
    chmod -R 755 "$PROJECT_DIR/dist"
fi
echo "✅ Permissions set"

# Test write access
echo ""
echo "4️⃣ Testing write access..."
TEST_FILE="$MEDIA_DIR/.test-write-$$"
if echo "test" > "$TEST_FILE" 2>/dev/null; then
    rm -f "$TEST_FILE"
    echo "✅ Write access confirmed"
else
    echo "❌ Still no write access!"
    echo ""
    echo "🔧 Try these commands manually:"
    echo "   sudo chown -R $(whoami):$(whoami) $PROJECT_DIR"
    echo "   sudo chmod -R 755 $PROJECT_DIR"
    echo "   sudo chmod 777 $MEDIA_DIR  # (less secure but should work)"
    exit 1
fi

echo ""
echo "🎉 Upload permissions should now be fixed!"
echo ""
echo "🚀 Next steps:"
echo "   1. Restart the server: npm start"
echo "   2. Test file upload in management interface"
echo "   3. If still failing, run: node scripts/diagnose-permissions.js"

echo ""
echo "📊 Current media directory status:"
ls -la "$MEDIA_DIR"
