#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * Diagnostic script to check static file serving setup
 * Run this on the Raspberry Pi to diagnose the CSS/JS loading issue
 */

function checkFileExists(filePath, description) {
    const exists = fs.existsSync(filePath);
    console.log(`${exists ? '✅' : '❌'} ${description}: ${filePath}`);
    if (exists) {
        const stats = fs.statSync(filePath);
        console.log(`   Size: ${stats.size} bytes, Modified: ${stats.mtime.toISOString()}`);
    }
    return exists;
}

function checkDirectory(dirPath, description) {
    const exists = fs.existsSync(dirPath);
    console.log(`${exists ? '✅' : '❌'} ${description}: ${dirPath}`);
    if (exists) {
        const files = fs.readdirSync(dirPath);
        console.log(`   Contains ${files.length} items: ${files.join(', ')}`);
    }
    return exists;
}

function main() {
    console.log('🔍 Cogiteon Player Static Files Diagnostic\n');
    
    const projectRoot = process.cwd();
    console.log(`Project root: ${projectRoot}\n`);
    
    // Check source files
    console.log('📁 Source Files:');
    checkDirectory(path.join(projectRoot, 'src', 'client'), 'Source client directory');
    checkFileExists(path.join(projectRoot, 'src', 'client', 'manage.html'), 'Source manage.html');
    checkFileExists(path.join(projectRoot, 'src', 'client', 'css', 'manage.css'), 'Source manage.css');
    checkFileExists(path.join(projectRoot, 'src', 'client', 'js', 'manage.js'), 'Source manage.js');
    
    console.log('\n📦 Dist Files:');
    checkDirectory(path.join(projectRoot, 'dist'), 'Dist directory');
    checkDirectory(path.join(projectRoot, 'dist', 'client'), 'Dist client directory');
    checkFileExists(path.join(projectRoot, 'dist', 'client', 'manage.html'), 'Dist manage.html');
    checkFileExists(path.join(projectRoot, 'dist', 'client', 'css', 'manage.css'), 'Dist manage.css');
    checkFileExists(path.join(projectRoot, 'dist', 'client', 'js', 'manage.js'), 'Dist manage.js');
    
    console.log('\n⚙️  Configuration:');
    checkFileExists(path.join(projectRoot, 'config', 'config.json'), 'Config file');
    
    if (fs.existsSync(path.join(projectRoot, 'config', 'config.json'))) {
        try {
            const config = JSON.parse(fs.readFileSync(path.join(projectRoot, 'config', 'config.json'), 'utf8'));
            console.log(`   Server port: ${config.server?.port || 'not set'}`);
            console.log(`   Server host: ${config.server?.host || 'not set'}`);
            console.log(`   Media directory: ${config.server?.mediaDirectory || 'not set'}`);
        } catch (error) {
            console.log(`❌ Failed to parse config: ${error.message}`);
        }
    }
    
    console.log('\n📋 Recommendations:');
    
    const distClientExists = fs.existsSync(path.join(projectRoot, 'dist', 'client'));
    const distCssExists = fs.existsSync(path.join(projectRoot, 'dist', 'client', 'css', 'manage.css'));
    const distJsExists = fs.existsSync(path.join(projectRoot, 'dist', 'client', 'js', 'manage.js'));
    
    if (!distClientExists) {
        console.log('❌ Dist client directory missing - run "npm run build" to create it');
    } else if (!distCssExists || !distJsExists) {
        console.log('❌ Some client files missing in dist - run "npm run build" to copy them');
    } else {
        console.log('✅ All required files exist');
        console.log('💡 If CSS/JS still not loading, check:');
        console.log('   1. Server is serving /static/ correctly');
        console.log('   2. No firewall blocking static file requests');
        console.log('   3. Check browser developer tools for 404 errors');
        console.log('   4. Verify server logs show static file requests');
    }
    
    console.log('\n🚀 Quick fixes:');
    console.log('   npm run build     # Rebuild and copy client files');
    console.log('   npm start         # Restart server');
    console.log('   curl http://localhost:3000/static/css/manage.css  # Test static file access');
}

if (require.main === module) {
    main();
}
