import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import { createServer } from 'http';
import { WebSocketServer } from 'ws';
import path from 'path';
import fs from 'fs';
import winston from 'winston';

import { Config } from '../types';
import { MediaService } from './services/media';
import { PJLinkService } from './services/pjlink';
import { ConfigService } from './services/config';
import { LoggerService } from './services/logger';

// Import routes
import mediaRoutes from './routes/media';
import projectorRoutes from './routes/projector';
import uploadRoutes from './routes/upload';
import apiRoutes from './routes/api';

class CogiteonPlayerServer {
  private app: express.Application;
  private server: any;
  private wss: WebSocketServer;
  private config: Config;
  private logger: winston.Logger;
  private mediaService: MediaService;
  private pjlinkService: PJLinkService;

  constructor() {
    this.app = express();
    this.initializeConfig();
    this.initializeLogger();
    this.initializeServices();
    this.initializeMiddleware();
    this.initializeRoutes();
    this.initializeWebSocket();
    this.initializeErrorHandling();
  }

  private initializeConfig(): void {
    try {
      this.config = ConfigService.loadConfig();
      console.log('Configuration loaded successfully');
    } catch (error) {
      console.error('Failed to load configuration:', error);
      process.exit(1);
    }
  }

  private initializeLogger(): void {
    this.logger = LoggerService.createLogger(this.config.logging);
    this.logger.info('Logger initialized');
  }

  private initializeServices(): void {
    this.mediaService = new MediaService(this.config.server, this.logger);

    if (this.config.projector.enabled) {
      this.pjlinkService = new PJLinkService(this.config.projector);
      this.logger.info('PJLink service initialized');
    }
  }

  private initializeMiddleware(): void {
    // Security middleware - Relaxed for media player external access
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'", "'unsafe-inline'"],
          imgSrc: ["'self'", "data:", "blob:"],
          mediaSrc: ["'self'", "blob:"],
          connectSrc: ["'self'", "ws:", "wss:", "*"] // Allow WebSocket connections from any origin
        }
      },
      crossOriginEmbedderPolicy: false // Allow external embedding
    }));

    // CORS - Allow all origins for media player access
    this.app.use(cors({
      origin: true, // Allow all origins for external device access
      credentials: true
    }));

    // Compression
    this.app.use(compression());

    // Rate limiting
    const limiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 100, // limit each IP to 100 requests per windowMs
      message: 'Too many requests from this IP'
    });
    this.app.use('/api', limiter);

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Request logging
    this.app.use((req, res, next) => {
      this.logger.info(`${req.method} ${req.path}`, {
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });
      next();
    });

    // Static files
    this.app.use('/static', express.static(path.join(__dirname, '../client')));
    this.app.use('/media', express.static(this.config.server.mediaDirectory));
  }

  private initializeRoutes(): void {
    // API routes
    this.app.use('/api', apiRoutes);
    this.app.use('/api/media', mediaRoutes);
    this.app.use('/upload', uploadRoutes);

    if (this.config.projector.enabled) {
      this.app.use('/projector', projectorRoutes);
    }

    // Main player route
    this.app.get('/', (req, res) => {
      res.sendFile(path.join(__dirname, '../client/player.html'));
    });

    // Management interface route
    this.app.get('/manage', (req, res) => {
      res.sendFile(path.join(__dirname, '../client/manage.html'));
    });

    // Health check
    this.app.get('/health', async (req, res) => {
      const health = {
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        mediaFiles: await this.mediaService.getMediaCount(),
        projector: this.config.projector.enabled ?
          await this.pjlinkService?.testConnection() : 'disabled'
      };
      res.json(health);
    });

    // API documentation
    this.app.get('/api', (req, res) => {
      res.json({
        name: 'Cogiteon Player API',
        version: '1.0.0',
        endpoints: {
          'GET /': 'Main player interface',
          'GET /manage': 'File management interface',
          'GET /health': 'Health check',
          'GET /api/media': 'List media files',
          'POST /upload': 'Upload media file',
          'DELETE /api/media/:filename': 'Delete media file',
          'POST /projector/on': 'Turn projector on',
          'POST /projector/off': 'Turn projector off',
          'GET /projector/status': 'Get projector status'
        }
      });
    });

    // 404 handler
    this.app.use('*', (req, res) => {
      res.status(404).json({
        success: false,
        error: 'Endpoint not found',
        path: req.originalUrl
      });
    });
  }

  private initializeWebSocket(): void {
    this.server = createServer(this.app);
    this.wss = new WebSocketServer({ server: this.server });

    this.wss.on('connection', (ws, req) => {
      this.logger.info('WebSocket connection established', { ip: req.socket.remoteAddress });

      // Send current player state
      this.sendPlayerState(ws);

      ws.on('message', async (message) => {
        try {
          const data = JSON.parse(message.toString());
          await this.handleWebSocketMessage(ws, data);
        } catch (error) {
          this.logger.error('WebSocket message error:', error);
          ws.send(JSON.stringify({ error: 'Invalid message format' }));
        }
      });

      ws.on('close', () => {
        this.logger.info('WebSocket connection closed');
      });

      ws.on('error', (error) => {
        this.logger.error('WebSocket error:', error);
      });
    });
  }

  private async handleWebSocketMessage(ws: any, data: any): Promise<void> {
    switch (data.type) {
      case 'getPlayerState':
        await this.sendPlayerState(ws);
        break;
      case 'getMediaList':
        const mediaList = await this.mediaService.getMediaFiles();
        ws.send(JSON.stringify({ type: 'mediaList', data: mediaList }));
        break;
      case 'refreshClient':
        // Broadcast refresh command to all connected clients
        this.broadcastToClients({ type: 'refresh' });
        this.logger.info('Client refresh requested from management interface');
        break;
      case 'projectorControl':
        if (this.pjlinkService && data.action) {
          const result = await this.handleProjectorControl(data.action);
          ws.send(JSON.stringify({ type: 'projectorResult', data: result }));
        }
        break;
      default:
        ws.send(JSON.stringify({ error: 'Unknown message type' }));
    }
  }

  private async sendPlayerState(ws: any): Promise<void> {
    try {
      const mediaFiles = await this.mediaService.getMediaFiles();
      const playerState = {
        mediaFiles,
        currentMedia: mediaFiles[0] || null,
        isPlaying: false,
        config: {
          slideshow: this.config.slideshow,
          audio: this.config.audio
        }
      };
      ws.send(JSON.stringify({ type: 'playerState', data: playerState }));
    } catch (error) {
      this.logger.error('Failed to send player state:', error);
    }
  }

  private broadcastToClients(message: any): void {
    this.wss.clients.forEach((client) => {
      if (client.readyState === client.OPEN) {
        client.send(JSON.stringify(message));
      }
    });
  }

  private async handleProjectorControl(action: string): Promise<any> {
    if (!this.pjlinkService) {
      return { success: false, error: 'Projector service not available' };
    }

    try {
      switch (action) {
        case 'on':
          const powerOn = await this.pjlinkService.powerOn();
          return { success: powerOn, action: 'power_on' };
        case 'off':
          const powerOff = await this.pjlinkService.powerOff();
          return { success: powerOff, action: 'power_off' };
        case 'status':
          const status = await this.pjlinkService.getStatus();
          return { success: true, action: 'status', data: status };
        default:
          return { success: false, error: 'Unknown action' };
      }
    } catch (error) {
      this.logger.error('Projector control error:', error);
      return { success: false, error: error.message };
    }
  }

  private initializeErrorHandling(): void {
    // Global error handler
    this.app.use((error: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
      this.logger.error('Unhandled error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    });

    // Process error handlers
    process.on('uncaughtException', (error) => {
      this.logger.error('Uncaught exception:', error);
      process.exit(1);
    });

    process.on('unhandledRejection', (reason, promise) => {
      this.logger.error('Unhandled rejection at:', promise, 'reason:', reason);
      process.exit(1);
    });

    // Graceful shutdown
    process.on('SIGTERM', () => {
      this.logger.info('SIGTERM received, shutting down gracefully');
      this.shutdown();
    });

    process.on('SIGINT', () => {
      this.logger.info('SIGINT received, shutting down gracefully');
      this.shutdown();
    });
  }

  private shutdown(): void {
    this.logger.info('Starting graceful shutdown...');

    this.server.close(() => {
      this.logger.info('HTTP server closed');
      process.exit(0);
    });

    // Force close after 10 seconds
    setTimeout(() => {
      this.logger.error('Could not close connections in time, forcefully shutting down');
      process.exit(1);
    }, 10000);
  }

  public start(): void {
    const port = this.config.server.port;
    const host = this.config.server.host;

    this.server.listen(port, host, () => {
      this.logger.info(`Cogiteon Player server started on ${host}:${port}`);
      this.logger.info(`Player interface: http://${host}:${port}/`);
      this.logger.info(`Management interface: http://${host}:${port}/manage`);
      this.logger.info(`API documentation: http://${host}:${port}/api`);
    });
  }
}

// Start the server
const server = new CogiteonPlayerServer();
server.start();
