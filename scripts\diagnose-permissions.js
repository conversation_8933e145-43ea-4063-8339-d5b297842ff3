#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const os = require('os');

/**
 * Comprehensive permission diagnostic script
 * Checks all aspects of file permissions that could affect uploads
 */

function checkPermissions(filePath, description) {
    try {
        const stats = fs.statSync(filePath);
        const mode = stats.mode;
        const permissions = {
            owner: {
                read: !!(mode & parseInt('400', 8)),
                write: !!(mode & parseInt('200', 8)),
                execute: !!(mode & parseInt('100', 8))
            },
            group: {
                read: !!(mode & parseInt('040', 8)),
                write: !!(mode & parseInt('020', 8)),
                execute: !!(mode & parseInt('010', 8))
            },
            others: {
                read: !!(mode & parseInt('004', 8)),
                write: !!(mode & parseInt('002', 8)),
                execute: !!(mode & parseInt('001', 8))
            }
        };
        
        const octal = (mode & parseInt('777', 8)).toString(8);
        
        console.log(`📁 ${description}: ${filePath}`);
        console.log(`   Permissions: ${octal} (${stats.isDirectory() ? 'd' : '-'}${
            permissions.owner.read ? 'r' : '-'}${
            permissions.owner.write ? 'w' : '-'}${
            permissions.owner.execute ? 'x' : '-'}${
            permissions.group.read ? 'r' : '-'}${
            permissions.group.write ? 'w' : '-'}${
            permissions.group.execute ? 'x' : '-'}${
            permissions.others.read ? 'r' : '-'}${
            permissions.others.write ? 'w' : '-'}${
            permissions.others.execute ? 'x' : '-'})`);
        console.log(`   Owner: ${stats.uid}, Group: ${stats.gid}`);
        console.log(`   Size: ${stats.size} bytes`);
        console.log(`   Modified: ${stats.mtime.toISOString()}`);
        
        return { exists: true, permissions, stats };
    } catch (error) {
        console.log(`❌ ${description}: ${filePath} - ${error.message}`);
        return { exists: false, error: error.message };
    }
}

function testWriteAccess(dirPath, description) {
    console.log(`\n🧪 Testing write access: ${description}`);
    
    const testFile = path.join(dirPath, `.write-test-${Date.now()}`);
    
    try {
        // Test write
        fs.writeFileSync(testFile, 'test write access');
        console.log(`✅ Write test successful`);
        
        // Test read
        const content = fs.readFileSync(testFile, 'utf8');
        if (content === 'test write access') {
            console.log(`✅ Read test successful`);
        } else {
            console.log(`❌ Read test failed - content mismatch`);
        }
        
        // Test delete
        fs.unlinkSync(testFile);
        console.log(`✅ Delete test successful`);
        
        return true;
    } catch (error) {
        console.log(`❌ Write access test failed: ${error.message}`);
        
        // Try to clean up if file was created
        try {
            if (fs.existsSync(testFile)) {
                fs.unlinkSync(testFile);
            }
        } catch (cleanupError) {
            console.log(`⚠️  Could not clean up test file: ${cleanupError.message}`);
        }
        
        return false;
    }
}

function checkNodeJsProcess() {
    console.log(`\n🔍 Node.js Process Information:`);
    console.log(`   Process ID: ${process.pid}`);
    console.log(`   User ID: ${process.getuid ? process.getuid() : 'N/A (Windows)'}`);
    console.log(`   Group ID: ${process.getgid ? process.getgid() : 'N/A (Windows)'}`);
    console.log(`   Working Directory: ${process.cwd()}`);
    console.log(`   Node.js Version: ${process.version}`);
    console.log(`   Platform: ${process.platform}`);
    console.log(`   Architecture: ${process.arch}`);
    
    if (process.getuid) {
        try {
            const userInfo = os.userInfo();
            console.log(`   Username: ${userInfo.username}`);
            console.log(`   Home Directory: ${userInfo.homedir}`);
            console.log(`   Shell: ${userInfo.shell || 'N/A'}`);
        } catch (error) {
            console.log(`   User Info Error: ${error.message}`);
        }
    }
}

function checkDiskSpace(dirPath) {
    console.log(`\n💾 Disk Space Check: ${dirPath}`);
    
    try {
        const stats = fs.statSync(dirPath);
        if (stats.isDirectory()) {
            // Try to get disk space (this is platform-specific)
            console.log(`   Directory exists and is accessible`);
            
            // Simple space check by trying to write a small file
            const testFile = path.join(dirPath, `.space-test-${Date.now()}`);
            try {
                const testData = 'x'.repeat(1024 * 1024); // 1MB test
                fs.writeFileSync(testFile, testData);
                fs.unlinkSync(testFile);
                console.log(`✅ Sufficient disk space (tested 1MB write)`);
                return true;
            } catch (error) {
                console.log(`❌ Disk space test failed: ${error.message}`);
                return false;
            }
        } else {
            console.log(`❌ Path is not a directory`);
            return false;
        }
    } catch (error) {
        console.log(`❌ Cannot access directory: ${error.message}`);
        return false;
    }
}

function main() {
    console.log('🔍 Cogiteon Player Permission Diagnostics');
    console.log('==========================================\n');
    
    const projectRoot = process.cwd();
    const mediaDir = path.join(projectRoot, 'media');
    const distDir = path.join(projectRoot, 'dist');
    const configFile = path.join(projectRoot, 'config', 'config.json');
    
    // Check Node.js process info
    checkNodeJsProcess();
    
    // Check directory permissions
    console.log(`\n📁 Directory Permission Checks:`);
    const projectCheck = checkPermissions(projectRoot, 'Project Root');
    const mediaCheck = checkPermissions(mediaDir, 'Media Directory');
    const distCheck = checkPermissions(distDir, 'Dist Directory');
    
    // Check important files
    console.log(`\n📄 File Permission Checks:`);
    checkPermissions(configFile, 'Config File');
    
    if (fs.existsSync(path.join(distDir, 'client'))) {
        checkPermissions(path.join(distDir, 'client'), 'Client Directory');
        checkPermissions(path.join(distDir, 'client', 'manage.html'), 'Management HTML');
    }
    
    // Test write access
    console.log(`\n🧪 Write Access Tests:`);
    const mediaWritable = mediaCheck.exists ? testWriteAccess(mediaDir, 'Media Directory') : false;
    const distWritable = distCheck.exists ? testWriteAccess(distDir, 'Dist Directory') : false;
    
    // Check disk space
    const mediaSpace = mediaCheck.exists ? checkDiskSpace(mediaDir) : false;
    
    // Load and check config
    console.log(`\n⚙️  Configuration Check:`);
    try {
        const config = JSON.parse(fs.readFileSync(configFile, 'utf8'));
        console.log(`✅ Config loaded successfully`);
        console.log(`   Server port: ${config.server?.port || 'not set'}`);
        console.log(`   Media directory: ${config.server?.mediaDirectory || 'not set'}`);
        console.log(`   Max file size: ${config.server?.maxFileSize || 'not set'}`);
        
        // Check if configured media directory matches actual
        const configMediaDir = path.resolve(config.server?.mediaDirectory || './media');
        const actualMediaDir = path.resolve(mediaDir);
        if (configMediaDir === actualMediaDir) {
            console.log(`✅ Media directory paths match`);
        } else {
            console.log(`⚠️  Media directory mismatch:`);
            console.log(`     Config: ${configMediaDir}`);
            console.log(`     Actual: ${actualMediaDir}`);
        }
    } catch (error) {
        console.log(`❌ Config error: ${error.message}`);
    }
    
    // Summary and recommendations
    console.log(`\n📋 Summary:`);
    console.log(`===========`);
    
    const issues = [];
    
    if (!mediaCheck.exists) {
        issues.push('Media directory does not exist');
    } else if (!mediaWritable) {
        issues.push('Media directory is not writable');
    }
    
    if (!distCheck.exists) {
        issues.push('Dist directory does not exist');
    }
    
    if (!mediaSpace) {
        issues.push('Insufficient disk space or media directory issues');
    }
    
    if (issues.length === 0) {
        console.log(`✅ All permission checks passed!`);
        console.log(`\n🚀 Upload should work. If it doesn't, check:`);
        console.log(`   1. Server logs for detailed error messages`);
        console.log(`   2. File size limits in configuration`);
        console.log(`   3. File type restrictions`);
        console.log(`   4. Network connectivity from client to server`);
    } else {
        console.log(`❌ Issues found:`);
        issues.forEach(issue => console.log(`   • ${issue}`));
        
        console.log(`\n🔧 Recommended fixes:`);
        console.log(`   1. Run: chmod 755 ${mediaDir}`);
        console.log(`   2. Run: chown $(whoami):$(whoami) ${mediaDir}`);
        console.log(`   3. Run: mkdir -p ${mediaDir} (if directory missing)`);
        console.log(`   4. Check disk space: df -h`);
        console.log(`   5. Run permission fix script: bash scripts/fix-permissions.sh`);
    }
    
    console.log(`\n🔧 Quick fix commands:`);
    console.log(`   sudo chown -R $(whoami):$(whoami) ${projectRoot}`);
    console.log(`   sudo chmod -R 755 ${projectRoot}`);
    console.log(`   mkdir -p ${mediaDir}`);
    console.log(`   chmod 755 ${mediaDir}`);
}

if (require.main === module) {
    main();
}
