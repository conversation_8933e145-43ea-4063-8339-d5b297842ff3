<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cogiteon Player</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html, body {
            width: 100%;
            height: 100%;
            background-color: #000;
            overflow: hidden;
            cursor: none;
            user-select: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
        }

        #media-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #000;
        }

        #media-element {
            max-width: 100vw;
            max-height: 100vh;
            width: auto;
            height: auto;
            object-fit: contain;
            display: none;
        }

        #loading-indicator {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #333;
            font-family: Arial, sans-serif;
            font-size: 24px;
            display: none;
        }

        #error-message {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #666;
            font-family: Arial, sans-serif;
            font-size: 18px;
            text-align: center;
            display: none;
        }

        /* Slideshow transition effects */
        .fade-transition {
            transition: opacity 1s ease-in-out;
        }

        .fade-out {
            opacity: 0;
        }

        .fade-in {
            opacity: 1;
        }

        /* Hide scrollbars */
        ::-webkit-scrollbar {
            display: none;
        }

        /* Disable text selection */
        ::selection {
            background: transparent;
        }

        ::-moz-selection {
            background: transparent;
        }
    </style>
</head>
<body>
    <div id="media-container">
        <video id="media-element" preload="auto" muted autoplay loop></video>
        <img id="media-element" style="display: none;" />

        <div id="loading-indicator">Loading media...</div>
        <div id="error-message">
            <div>No media available</div>
            <div style="font-size: 14px; margin-top: 10px;">Add media files via the management interface</div>
        </div>
    </div>

    <script>
        class CogiteonPlayer {
            constructor() {
                this.mediaContainer = document.getElementById('media-container');
                this.loadingIndicator = document.getElementById('loading-indicator');
                this.errorMessage = document.getElementById('error-message');

                this.currentMediaIndex = 0;
                this.mediaFiles = [];
                this.isPlaying = false;
                this.slideshowTimer = null;
                this.config = {
                    slideshow: {
                        imageDuration: 5000,
                        transitionDuration: 1000
                    }
                };

                this.websocket = null;
                this.reconnectAttempts = 0;
                this.maxReconnectAttempts = 10;

                this.init();
            }

            async init() {
                console.log('Initializing Cogiteon Player...');

                // Disable context menu
                document.addEventListener('contextmenu', e => e.preventDefault());

                // Disable keyboard shortcuts
                document.addEventListener('keydown', e => e.preventDefault());

                // Disable mouse interactions
                document.addEventListener('selectstart', e => e.preventDefault());
                document.addEventListener('dragstart', e => e.preventDefault());

                // Connect to WebSocket
                this.connectWebSocket();

                // Load initial media
                await this.loadMediaList();

                // Start playback
                this.startPlayback();
            }

            connectWebSocket() {
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const wsUrl = `${protocol}//${window.location.host}`;

                try {
                    this.websocket = new WebSocket(wsUrl);

                    this.websocket.onopen = () => {
                        console.log('WebSocket connected');
                        this.reconnectAttempts = 0;
                        this.requestPlayerState();
                    };

                    this.websocket.onmessage = (event) => {
                        this.handleWebSocketMessage(JSON.parse(event.data));
                    };

                    this.websocket.onclose = () => {
                        console.log('WebSocket disconnected');
                        this.scheduleReconnect();
                    };

                    this.websocket.onerror = (error) => {
                        console.error('WebSocket error:', error);
                    };
                } catch (error) {
                    console.error('Failed to connect WebSocket:', error);
                    this.scheduleReconnect();
                }
            }

            scheduleReconnect() {
                if (this.reconnectAttempts < this.maxReconnectAttempts) {
                    this.reconnectAttempts++;
                    const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);
                    console.log(`Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts})`);
                    setTimeout(() => this.connectWebSocket(), delay);
                }
            }

            handleWebSocketMessage(message) {
                switch (message.type) {
                    case 'playerState':
                        this.updatePlayerState(message.data);
                        break;
                    case 'mediaList':
                        this.updateMediaList(message.data);
                        break;
                    case 'refresh':
                        console.log('Refresh requested from management interface');
                        this.refreshPlayer();
                        break;
                    default:
                        console.log('Unknown WebSocket message:', message);
                }
            }

            requestPlayerState() {
                if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
                    this.websocket.send(JSON.stringify({ type: 'getPlayerState' }));
                }
            }

            updatePlayerState(state) {
                this.mediaFiles = state.mediaFiles || [];
                this.config = { ...this.config, ...state.config };
                this.startPlayback();
            }

            updateMediaList(mediaFiles) {
                this.mediaFiles = mediaFiles || [];
                this.currentMediaIndex = 0;
                this.startPlayback();
            }

            async refreshPlayer() {
                console.log('Refreshing player...');
                this.clearCurrentMedia();
                this.showLoading();
                await this.loadMediaList();
                this.startPlayback();
            }

            async loadMediaList() {
                try {
                    const response = await fetch('/api/media');
                    const data = await response.json();
                    if (data.success) {
                        this.mediaFiles = data.data || [];
                    }
                } catch (error) {
                    console.error('Failed to load media list:', error);
                }
            }

            startPlayback() {
                this.clearSlideshowTimer();

                if (!this.mediaFiles || this.mediaFiles.length === 0) {
                    this.showError();
                    return;
                }

                this.hideError();
                this.hideLoading();

                if (this.mediaFiles.length === 1) {
                    this.playSingleMedia();
                } else {
                    this.startSlideshow();
                }
            }

            playSingleMedia() {
                const media = this.mediaFiles[0];
                if (media.type === 'video') {
                    this.playVideo(media, true); // Loop single video
                } else {
                    this.showImage(media, 0); // Show single image indefinitely
                }
            }

            startSlideshow() {
                this.currentMediaIndex = 0;
                this.playCurrentMedia();
            }

            playCurrentMedia() {
                if (!this.mediaFiles || this.mediaFiles.length === 0) {
                    this.showError();
                    return;
                }

                const media = this.mediaFiles[this.currentMediaIndex];

                if (media.type === 'video') {
                    this.playVideo(media, false);
                } else {
                    const duration = this.config.slideshow.imageDuration;
                    this.showImage(media, duration);
                }
            }

            playVideo(media, loop = false) {
                this.clearCurrentMedia();

                const video = document.createElement('video');
                video.id = 'current-media';
                video.src = `/media/${media.filename}`;
                video.autoplay = true;
                video.muted = true;
                video.loop = loop;
                video.style.maxWidth = '100vw';
                video.style.maxHeight = '100vh';
                video.style.width = 'auto';
                video.style.height = 'auto';
                video.style.objectFit = 'contain';

                video.onended = () => {
                    if (!loop) {
                        this.nextMedia();
                    }
                };

                video.onerror = () => {
                    console.error('Video playback error:', media.filename);
                    this.nextMedia();
                };

                this.mediaContainer.appendChild(video);
                this.isPlaying = true;
            }

            showImage(media, duration) {
                this.clearCurrentMedia();

                const img = document.createElement('img');
                img.id = 'current-media';
                img.src = `/media/${media.filename}`;
                img.style.maxWidth = '100vw';
                img.style.maxHeight = '100vh';
                img.style.width = 'auto';
                img.style.height = 'auto';
                img.style.objectFit = 'contain';
                img.className = 'fade-transition fade-in';

                img.onload = () => {
                    if (duration > 0) {
                        this.slideshowTimer = setTimeout(() => {
                            this.nextMedia();
                        }, duration);
                    }
                };

                img.onerror = () => {
                    console.error('Image load error:', media.filename);
                    this.nextMedia();
                };

                this.mediaContainer.appendChild(img);
                this.isPlaying = true;
            }

            nextMedia() {
                if (this.mediaFiles.length <= 1) {
                    return;
                }

                this.currentMediaIndex = (this.currentMediaIndex + 1) % this.mediaFiles.length;
                this.playCurrentMedia();
            }

            clearCurrentMedia() {
                const currentMedia = document.getElementById('current-media');
                if (currentMedia) {
                    currentMedia.remove();
                }
                this.clearSlideshowTimer();
            }

            clearSlideshowTimer() {
                if (this.slideshowTimer) {
                    clearTimeout(this.slideshowTimer);
                    this.slideshowTimer = null;
                }
            }

            showLoading() {
                this.loadingIndicator.style.display = 'block';
                this.errorMessage.style.display = 'none';
            }

            hideLoading() {
                this.loadingIndicator.style.display = 'none';
            }

            showError() {
                this.errorMessage.style.display = 'block';
                this.loadingIndicator.style.display = 'none';
                this.clearCurrentMedia();
                this.isPlaying = false;
            }

            hideError() {
                this.errorMessage.style.display = 'none';
            }
        }

        // Start the player when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new CogiteonPlayer();
        });

        // Prevent page refresh and navigation
        window.addEventListener('beforeunload', (e) => {
            e.preventDefault();
            return '';
        });

        // Disable F5, Ctrl+R, etc.
        document.addEventListener('keydown', (e) => {
            if (e.key === 'F5' || (e.ctrlKey && e.key === 'r')) {
                e.preventDefault();
            }
        });
    </script>
</body>
</html>
