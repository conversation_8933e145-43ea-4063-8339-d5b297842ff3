{"name": "cogiteon-player", "version": "1.0.0", "description": "TypeScript-based media player for Raspberry Pi with kiosk mode and projector control", "main": "dist/server/app.js", "scripts": {"dev": "concurrently \"npm run build:watch\" \"npm run start:dev\"", "build": "node scripts/build.js", "build:ts": "tsc --noEmitOnError false", "build:client": "npm run copy-client", "build:watch": "concurrently \"tsc --watch\" \"npm run copy-client:watch\"", "copy-client": "node scripts/copy-client.js", "copy-client:watch": "nodemon --watch src/client --ext html,css,js --exec \"npm run copy-client\"", "start": "node dist/server/app.js", "start:dev": "nodemon dist/server/app.js", "kiosk": "bash scripts/start-kiosk.sh", "install-service": "bash scripts/install-service.sh", "test": "jest", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["media-player", "raspberry-pi", "kiosk", "pjlink", "typescript", "chromium"], "author": "Cogiteon Player Team", "license": "MIT", "dependencies": {"compression": "^1.7.4", "cors": "^2.8.5", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "fluent-ffmpeg": "^2.1.2", "helmet": "^7.1.0", "mime-types": "^2.1.35", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "winston": "^3.11.0", "ws": "^8.14.2"}, "devDependencies": {"@types/compression": "^1.7.4", "@types/cors": "^2.8.15", "@types/express": "^4.17.20", "@types/fluent-ffmpeg": "^2.1.24", "@types/jest": "^29.5.6", "@types/mime-types": "^2.1.4", "@types/multer": "^1.4.8", "@types/node": "^20.8.0", "@types/ws": "^8.5.8", "@typescript-eslint/eslint-plugin": "^6.8.0", "@typescript-eslint/parser": "^6.8.0", "concurrently": "^8.2.2", "eslint": "^8.51.0", "jest": "^29.7.0", "nodemon": "^3.0.1", "ts-jest": "^29.1.1", "typescript": "^5.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}